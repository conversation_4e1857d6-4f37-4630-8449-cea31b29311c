{"name": "assessement", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@floating-ui/vue": "^1.0.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^2.0.10", "@guolao/vue-monaco-editor": "^1.5.5", "axios": "^1.11.0", "core-js": "^3.8.3", "face-api.js": "^0.22.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "pinia": "^2.1.7", "sass": "^1.77.6", "sass-loader": "^13.2.0", "vue": "^2.6.14", "vue-cookies": "^1.8.3", "vue-highlightjs": "^1.3.3", "vue-lottie": "^0.2.1", "vue-simple-accordion": "^0.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.17", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}