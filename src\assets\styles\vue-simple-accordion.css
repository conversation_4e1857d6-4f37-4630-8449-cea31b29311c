.vsa-item__heading{
width:100%;
height:100%
}
.vsa-item__heading,.vsa-item__trigger{
display:flex;
justify-content:flex-start;
align-items:center
}
.vsa-item__trigger{
margin:0;
padding:0;
color:inherit;
font-family:inherit;
font-size:100%;
/* line-height:1.15; */
border-width:0;
background-color:transparent;
background-image:none;
overflow:visible;
text-transform:none;
flex:1 1 auto;
color:var(--vsa-text-color);
transition:all .2s linear;
padding:var(--vsa-heading-padding)
}
.vsa-item__trigger[role=button]{
cursor:pointer
}
.vsa-item__trigger[type=button],.vsa-item__trigger[type=reset],.vsa-item__trigger[type=submit]{
-webkit-appearance:button
}
.vsa-item__trigger:focus{
outline:1px dotted;
outline:5px auto -webkit-focus-ring-color
}
.vsa-item__trigger::-moz-focus-inner,.vsa-item__trigger[type=button]::-moz-focus-inner,.vsa-item__trigger[type=reset]::-moz-focus-inner,.vsa-item__trigger[type=submit]::-moz-focus-inner{
border-style:none;
padding:0
}
.vsa-item__trigger:-moz-focusring,.vsa-item__trigger[type=button]:-moz-focusring,.vsa-item__trigger[type=reset]:-moz-focusring,.vsa-item__trigger[type=submit]:-moz-focusring{
outline:1px dotted ButtonText
}
.vsa-item__trigger:focus,.vsa-item__trigger:hover{
outline:none;
background-color:var(--vsa-highlight-color);
color:var(--vsa-bg-color)
}
.vsa-item__trigger__icon--is-default{
width:0px;
height:15px;
transform:scale(var(--vsa-default-icon-size))
}
.vsa-item__trigger__icon--is-default:after,.vsa-item__trigger__icon--is-default:before{
background-color:var(--vsa-text-color);
content:"";
height:2px;
position:absolute;
top:-8px;
transition:all .13333s ease-in-out;
width:10px
}
.vsa-item__trigger__icon--is-default:before{
left:1px;
transform:rotate(45deg) translate3d(8px,21px,0);
transform-origin:100%
}
.vsa-item__trigger__icon--is-default:after{
transform:rotate(-45deg) translate3d(-8px,21px,0);
right:12px;
transform-origin:0
}
.vsa-item__trigger[aria-expanded=true] .vsa-item__trigger__icon--is-default:before{
transform:rotate(45deg) translate3d(8px,21px,0);
left: -6px;

}
.vsa-item__trigger[aria-expanded=true] .vsa-item__trigger__icon--is-default:after{
transform:rotate(-45deg) translate3d(-8px,21px,0);
right: 5px;
}
.vsa-item__trigger__icon{
display:block;
margin-left:auto;
position:relative;
transition:all .2s ease-in-out
}
.vsa-item__trigger:focus .vsa-item__trigger__icon--is-default:after,.vsa-item__trigger:focus .vsa-item__trigger__icon--is-default:before,.vsa-item__trigger:hover .vsa-item__trigger__icon--is-default:after,.vsa-item__trigger:hover .vsa-item__trigger__icon--is-default:before{
background-color:var(--vsa-bg-color)
}
.vsa-item__trigger__content{
font-weight:400;
font-size: 18px
}
.vsa-item__content{
margin:0;
/* padding:var(--vsa-content-padding) */
}
.vsa-item--is-active .vsa-item__heading,.vsa-item:not(:last-of-type){
border-bottom:var(--vsa-border)
}
.vsa-collapse-enter-active,.vsa-collapse-leave-active{
transition-property:opacity,height,padding-top,padding-bottom;
transition-duration:.3s;
transition-timing-function:ease-in-out
}
.vsa-collapse-enter,.vsa-collapse-leave-active{
opacity:0;
height:0;
padding-top:0;
padding-bottom:0;
overflow:hidden
}
.vsa-list{
--vsa-max-width:720px;
--vsa-min-width:300px;
--vsa-heading-padding:1rem .5rem;
--vsa-text-color:#373737;
--vsa-highlight-color:#3361ff;
--vsa-bg-color:#fff;
--vsa-border-color:rgba(0,0,0,0.2);
--vsa-border-width:1px;
--vsa-border-style:solid;
--vsa-border:var(--vsa-border-width) var(--vsa-border-style) var(--vsa-border-color);
--vsa-content-padding:1rem 1rem;
--vsa-default-icon-size:1;
display:block;
width:100%;
padding:0;
margin:0;
list-style:none;
border:var(--vsa-border);
color:var(--vsa-text-color);
background-color:var(--vsa-bg-color)
}
.vsa-list [hidden]{
display:none
}
