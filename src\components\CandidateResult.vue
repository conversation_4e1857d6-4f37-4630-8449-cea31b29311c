<template>
    <div class="resultPage">
        <div class="candidate-name">
            {{ candidateName }},Thank You for passing the test.
        </div>
        <div class="result-cards">
            <div class="res-card">Your score is <span>{{ candidateScore }}%</span> </div>
            <div class="res-card">You ranked on top <span>{{ candidateRank_rescaled_100 }}%</span></div>
        </div>

        <ToolTip placement="top" :content="`you randked on the top ${candidateRank_rescaled_100}% of candidates`">
            <div class="rankScale">

                <font-awesome-icon class="faIcon" :icon="['fas', 'person']" v-for="scale in 10" :key="scale"
                    :class="{ colored: scale <= candidateRank_rescaled_10 }" />

            </div>
        </ToolTip>
    </div>
</template>

<script>
import ToolTip from "./ToolTip.vue"
export default {
    name: "CandidateResult",
    components: { ToolTip },
    props: {
        candidateCount: { type: Number }, candidateRank: { type: Number },
        candidateScore: { type: Number }, candidateName: { type: String }
    },
    data() {
        return {
            /*candidateCount: 122,
            candidateRank: 36,*/
        }
    },
    computed: {
        candidateRank_rescaled_10() {
            return Math.round(this.candidateRank * 10 / this.candidateCount);
        },
        candidateRank_rescaled_100() {
            return Math.floor(this.candidateRank * 10000 / this.candidateCount) / 100;
        },
    }
}
</script>

<style lang="scss" scoped>
.resultPage {
    width: 100%;
    height: 100%;
    line-height: 50px;
    font-weight: 600;
    color: #343637;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2% 0;
    gap: 2rem;

    .candidate-name {
        font-size: 42px;
    }

    .result-cards {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: 50%;

        .res-card {
            background: linear-gradient(135deg, #868CFF 0%, #00aef0 100%);
            ;
            border: none;
            border-radius: 10px;
            box-shadow: 0px 0px 10px #abaaaa;
            display: flex;
            flex-direction: column;
            flex: 0 0 35%;
            color: #fff;

            >span {
                font-size: 50px;
                //color: #3361ff;
            }
        }
    }

    .rankScale {
        height: 250px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background: #fff;
        padding: 0 5%;
        border-radius: 8px;

        .faIcon {
            width: 8%;
            height: 100%;
            font-size: 8px;
            color: #c1c1c1;
        }

        .colored {
            color: #00aef0;
        }
    }
}
</style>