<template>
    <div class="popup-container">
        <div class="popup-message">
            <p>
                Please face your webcam to continue the test,
                the clock is running
            </p>
        </div>
    </div>
</template>
  
<script>


export default {
    name: "FaceDetectionPopUp",
    data() {
        return {

        };
    },

};
</script>
  
<style lang="scss" scoped>
.popup-container {
    height: 100%;
    width: 100%;
    display: flex;
    padding: 0;
    margin: 0;
    align-items: center;
    justify-content: center;
    background: rgba(128, 128, 128, 0.568);
    z-index: 2000;
    overflow-y: hidden;
    position: fixed;
}

.popup-message {
    display: flex;
    padding: 2rem;
    border-radius: 5px;
    justify-content: center;
    align-items: center;
    background: #fff;
    border: 1px solid #EC8E7B;
    width: 40%;
    height: 30%;
    z-index: 2001;

}

p {
    color: #D13415;
    font-family: "Roboto";
    font-style: normal;
    font-size: 2rem;
    text-align: center;
}
</style>
  