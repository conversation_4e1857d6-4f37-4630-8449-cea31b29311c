<template>
  <aside v-if="qstList">
    Remaining Time :
    <!-- <testTimer :duration="duration" :start="startTimer" /> -->
    <h1>Questions</h1>
    <div>
      <vsa-list :auto-collapse="false">
        <TestSection
          v-for="test in testInfos"
          :key="test.name"
          :header="test.name"
        >
          <ul>
            <li v-for="qst in test.qstCount" :key="`${test.name}Qst${qst}`">
              <CheckQuestion
                :name="`${test.name}Qst${qst}`"
                :id="`${test.name}Qst${qst}`"
                :testNum="test.index"
                :qstNum="qst"
              />
              Question {{ qst }}
              <!-- <input type="checkbox" :name="`${test.name}Qst${qst}` " :id="`${test.name}Qst${qst}` " disabled :checked=" {qst<=qstNum }"> Question {{ qst }} -->
            </li>
          </ul>
        </TestSection>
      </vsa-list>
    </div>
  </aside>
</template>

<script>
// import testTimer from "./timer.vue";
import { useAssessementStore } from "@/store/assessements";
import TestSection from "./testSection.vue";
import { VsaList } from "vue-simple-accordion";
import CheckQuestion from "./checkQuestion.vue";
import "../assets/styles/vue-simple-accordion.css";

export default {
  name: "SideBar",
  props: {
    qstList: { type: Boolean, default: false },
  },
  components: { /* testTimer, */ TestSection, CheckQuestion, VsaList },
  data() {
    return {
      duration: this.assessementStore.questionTime,
      startTimer: false,
    };
  },
  computed: {
    testInfos() {
      return this.assessementStore.metaInfos;
    },
  },
  setup() {
    const assessementStore = useAssessementStore();
    return { assessementStore };
  },
  watch: {
    duration: function (newVal) {
      if (newVal) {
        console.log({ newVal });
      }
    },
  },
  mounted() {
    // setTimeout(() => {
    //   this.duration = this.assessementStore.userTimer;
    // }, 3000);
    // this.duration = this.assessementStore.questionTime;
    this.startTimer = true;
    // this.assessementNames= this.assessementStore.assessements.map(el=>{return {name: el.name, qstCount: el.questions_list.length}})
  },
};
</script>

<style lang="scss" scoped>
aside {
  text-align: center;
  float: right;
  // height: 100%;
  width: 30rem;
  background: #fff;
  font-family: "Poppins";
  padding: 1rem;

  ul {
    text-align: left;
    padding-left: 1rem;

    li {
      list-style: none;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      margin: 4px 0;

      /* input[type="checkbox"]:disabled {
                cursor: default;
                appearance: none; 
                height: 16px;
                width: 16px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            input[type="checkbox"]:checked{
                background-color: #000;
                // box-shadow: 0 0 0 2px #e6e6e6;
            } */
    }
  }
}
</style>
