<template>
    <div class="inline-block" @mousemove="updatePosition">
      <div
        ref="referenceRef"
        class="inline-block"
        @blur="hide"
        @focus="show"
        @mouseenter="show"
        @mouseleave="hide"
      >
        <slot></slot>
      </div>
      <div
        ref="floatingRef"
        :class="[
          'floating-tooltip',
          isHidden && 'hidden',
        ]"
        :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
      >
        {{ content }}
        <div ref="arrowRef" class="arrow"></div>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, onMounted } from "vue";
  //import { arrow, computePosition, flip, offset, shift } from "@floating-ui/dom";
  
  export default {
    name: 'ToolTip',
    props: {
      content: { type: String },
      placement: {
        type: String,
        default: "bottom",
      },
    },
    setup(/*props*/) {
      const referenceRef = ref();
      const floatingRef = ref();
      const arrowRef = ref();
      const isHidden = ref(true);
      const tooltipX = ref(0);
      const tooltipY = ref(0);
  
      onMounted(() => {
        calculatePosition();
      });
  
      function calculatePosition() {
        const rect = referenceRef.value.getBoundingClientRect();
        tooltipX.value = rect.left + window.pageXOffset;
        tooltipY.value = rect.top + window.pageYOffset;
      }
  
      function hide() {
        isHidden.value = true;
      }
  
      function show() {
        isHidden.value = false;
        calculatePosition();
      }
  
      function updatePosition(event) {
        tooltipX.value = event.pageX;
        tooltipY.value = event.pageY;
      }
  
      return {
        referenceRef,
        floatingRef,
        arrowRef,
        isHidden,
        hide,
        show,
        tooltipX,
        tooltipY,
        updatePosition,
      };
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .inline-block {
    display: inline-block;
  }
  
  .floating-tooltip {
    position: fixed;
    z-index: 50;
    background-color: #4a5568;
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    cursor: default;
    pointer-events: none;
    opacity: 90%;
  }
  
  .hidden {
    display: none;
  }
  
  .arrow {
    position: absolute;
    background-color: #4a5568;
    height: 8px;
    width: 8px;
    transform: rotate(45deg);
  }
  </style>
  
 <!--<template>
    <div class="inline-block">
      <div
        ref="referenceRef"
        class="inline-block"
        @blur="hide"
        @focus="show"
        @mouseenter="show"
        @mouseleave="hide"
      >
        <slot></slot>
      </div>
      <div
        ref="floatingRef"
        :class="[
          'floating-tooltip',
          isHidden && 'hidden',
        ]"
      >
        {{ content }}
        <div ref="arrowRef" class="arrow"></div>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, onMounted } from "vue";
  import { arrow, computePosition, flip, offset, shift } from "@floating-ui/dom";
  
  export default {
    name: 'ToolTip',
    props: {
      content: {type: String},
      placement: {
        type: String,
        default: "bottom",
      },
    },
    setup(props) {
      const referenceRef = ref();
      const floatingRef = ref();
      const arrowRef = ref();
      const isHidden = ref(true);
  
      onMounted(() => {
        calculatePosition();
      });
  
      async function calculatePosition() {
        const { x, y, middlewareData, placement } = await computePosition(
          referenceRef.value,
          floatingRef.value,
          {
            placement: props.placement,
            middleware: [
              offset(8),
              flip(),
              shift({ padding: 5 }),
              arrow({ element: arrowRef.value }),
            ],
          }
        );
  
        Object.assign(floatingRef.value.style, {
          left: `${x}px`,
          top: `${y}px`,
        });
  
        const { x: arrowX, y: arrowY } = middlewareData.arrow;
  
        const opposedSide = {
          left: "right",
          right: "left",
          bottom: "top",
          top: "bottom",
        }[placement.split("-")[0]];
  
        Object.assign(arrowRef.value.style, {
          left: arrowX ? `${arrowX}px` : "",
          top: arrowY ? `${arrowY}px` : "",
          bottom: "",
          right: "",
          [opposedSide]: "-4px",
        });
      }
  
      function hide() {
        isHidden.value = true;
      }
  
      function show() {
        isHidden.value = false;
        calculatePosition();
      }
  
      return {
        referenceRef,
        floatingRef,
        arrowRef,
        isHidden,
        hide,
        show,
      };
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .inline-block {
    display: inline-block;
  }
  
  .floating-tooltip {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;
    background-color: #4a5568;
    color: white;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    cursor: default;
  }
  
  .hidden {
    display: none;
  }
  
  .arrow {
    position: absolute;
    background-color: #4a5568;
    height: 8px;
    width: 8px;
    transform: rotate(45deg);
  }
  </style>
 -->