<template>
    <input type="checkbox" :name="name " :id="id " disabled :checked=" qstChecked"> 
    <!-- :current-test="testNum" :current-qst="qstNum" :qst-num="qst" :test-num="index" -->
</template>

<script>
import { useAssessementStore } from '@/store/assessements';
    export default {
        name:"CheckQuestion",
        props:['name','id','qstNum', 'testNum'],
        computed:{
            qstChecked(){
                return ((this.qstNum < this.assessementStore.question && this.testNum === this.assessementStore.test) || this.testNum< this.assessementStore.test)
            }
        },
        setup(){
            const assessementStore = useAssessementStore()
            return {assessementStore}
        },
    }
</script>

<style lang="scss" scoped>
input[type="checkbox"]:disabled {
                cursor: default;
                appearance: none; 
                height: 16px;
                width: 16px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            input[type="checkbox"]:checked{
                background-color: #000;
                // box-shadow: 0 0 0 2px #e6e6e6;
            }
</style>