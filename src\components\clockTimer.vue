<template>
    <div class="cloader">
        <div class="clface">
            <div class="clsface">
                <div id="h2" class="hand"></div>
            </div>
            <div class="top"></div>
            <div class="bottom"></div>
            <div class="left"></div>
            <div class="right"></div>
            <div id="sub" class="pin"></div>
            <div id="h1" class="hand"></div>
            <div id="main" class="pin"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'clockTimer',

}
</script>

<style scoped>
.cloader {
    width: 6em;
    /* Adjusted width */
    height: 6em;
    /* Adjusted height */
    border: 2px solid white;
    /* Adjusted border */
    border-radius: 50%;
    margin: 50px auto;
    position: relative;
    padding: 1rem;
    /* Adjusted padding */
}

.clface {
    position: relative;
    width: 100%;
    height: 100%;
    transform: translateY(-1px);
    /* Adjusted translateY */
}

.top {
    height: 10px;
    width: 4px;
    content: '';
    background-color: black;
    border-radius: 5em;
    margin-top: -30px;
    margin-left: 42px;
}

.bottom {
    height: 10px;
    width: 4px;
    content: '';
    background-color: black;
    border-radius: 5em;
    margin-top: 135px;
    margin-left: 42px;
}

.left {
    height: 4px;
    width: 10px;
    content: '';
    background-color: black;
    border-radius: 5em;
    margin-top: -82px;
    margin-left: 112px;
}

.right {
    height: 4px;
    width: 10px;
    content: '';
    background-color: black;
    border-radius: 5em;
    margin-top: -4px;
    margin-left: -35px;
}

.clsface {
    position: absolute;
    width: 10px;
    height: 0px;
    margin-left: 24px;
    margin-top: 15px;
    border-radius: 6em;
    border: 2px solid #636363;
}

.hand {
    width: 65%;
    height: 4px;
    background-color: black;
    border-radius: 3em;
    border: none;
    position: absolute;
    top: 85%;
    left: -15%;
    transform-origin: 100%;
    transform: rotate(90deg);
}

.pin {
    width: 25%;
    height: 25%;
    border-radius: 50%;
    background: black;
    position: absolute;
    top: 86%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#sub {
    width: 10%;
    height: 10%;
    border-radius: 50%;
    background: #636363;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#h1 {
    animation: rotate 4s linear infinite;
}

#h2 {
    width: 45%;
    left: 5%;
    top: 45%;
    background-color: #636363;
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(90deg);
    }

    to {
        transform: rotate(450deg);
    }
}
</style>