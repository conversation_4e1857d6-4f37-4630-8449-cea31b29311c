<template>
    <div class="flex flex-col h-3/5">
        <div class="flex items-center p-3 bg-gray-100 rounded-tr-md border-b border-gray-300">
            <select v-model="selectedLanguage"
                class="px-3 py-2 text-gray-800 bg-gray-200 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                @change="handleLanguageChange">
                <option v-for="lang in languages" :key="lang.id" :value="lang.id">
                    {{ lang.name }}
                </option>
            </select>

            <div class="flex ml-auto space-x-2">
                <button
                    class="px-4 py-2 text-white bg-NeonBlue rounded-md hover:opacity-85 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    @click="handleRun" :disabled="running">
                    <svg v-if="running" class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    {{ running ? "Running..." : "Run Code" }}
                </button>

                <button
                    class="px-4 py-2 text-white bg-green-600 rounded-md hover:opacity-85 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    @click="handleSubmit" :disabled="submitting">
                    <svg v-if="submitting" class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    {{ submitting ? "Submitting..." : "Submit Code" }}
                </button>
            </div>
        </div>

        <div class="flex-1">
            <VueMonacoEditor v-model="code" :language="selectedLanguage" :options="editorOptions" theme="vs-light"
                class="h-full" />
        </div>
    </div>
</template>

<script>
import { VueMonacoEditor } from "@guolao/vue-monaco-editor";
import axios from "axios";
import { buildApiUrl, API_CONFIG } from "../../constants/constants.js";
import { useAssessementStore } from "@/store/assessements";

export default {
    components: { VueMonacoEditor },
    props: {
        problem: {
            type: Object,
            default: () => ({}),
        },
        starterCode: {
            type: Object,
            required: true,
            default: () => ({}),
        },
        running: {
            type: Boolean,
            default: false,
        },
    },
    setup() {
        const assessementStore = useAssessementStore();
        return { assessementStore };
    },
    data() {
        return {
            selectedLanguage: "javascript",
            code: this.starterCode.javascript,
            editorOptions: {
                automaticLayout: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineNumbers: "on",
                wordWrap: "on",
                tabSize: 2,
                detectIndentation: true,
            },
            problem1: [],
            idCandt: "",
            idProj: "",
            languages: [
                { id: "python", name: "Python" },
                { id: "javascript", name: "JavaScript" },
                { id: "java", name: "Java" },
                { id: "cpp", name: "C++" },
            ],
            submitting: false
        };
    },
    watch: {
        selectedLanguage(newLang) {
            if (this.code === this.starterCode[this.previousLanguage]) {
                // Only auto-switch code if user hasn't made changes
                this.code = this.starterCode[newLang];
            }
            this.$emit("language-change", newLang);
            this.previousLanguage = newLang;
        },
    },
    created() {
        this.previousLanguage = this.selectedLanguage;
    },
    async mounted() {
        await this.assessementStore.fetchAssessments();
        this.problem1 = await this.assessementStore.challenge;
        this.idProj = await this.assessementStore.projectId; 
        await this.assessementStore.getAvatar();
        this.idCandt = await this.assessementStore.candidate_id; 
    },
    methods: {
        handleRun() {
            this.$emit("run-code", this.code);
        },
        async handleSubmit() {
            this.submitting = true;
            var lang = this.selectedLanguage;
            var problem = this.problem1?.question;
            var code = this.code;
            var idCandidate = this.idCandt;
            var idProject = this.idProj;
            
            try {
                const response = await axios.post(buildApiUrl(API_CONFIG.ENDPOINTS.EVALUATE_CODE),
                    {
                        lang,
                        problem,
                        code,
                        idCandidate,
                        idProject
                    },
                    {
                        headers: {
                            "Content-Type": "application/json",
                        },
                    },
                );
                console.log(response);
            } finally {
                this.submitting = false;
            }
        },
        handleLanguageChange() {
            this.code = this.starterCode[this.selectedLanguage];
            this.$emit("language-change", this.selectedLanguage);
        },
    },
};
</script>