<!-- App.vue -->
<template>
  <div class="w-full flex flex-col item-center rounded-md border border-gray-300 bg-white shadow-[0_0_15px_0_rgba(0,0,0,15%)]">
    <main class="flex flex-1 w-full">
      <ProblemDescription :problem="challenge?.question" />
      <div class="flex-1 flex flex-col">
        <CodeEditor :problem="challenge?.question" :starter-code="currentProblem.starterCode" @language-change="handleLanguageChange" @run-code="runCode" :running="outputStatus === 'running'" />
        <OutputPanel :content="outputContent" :status="outputStatus" :status-text="outputStatusText" />
      </div>
    </main>
  </div>
</template>

<script>
import ProblemDescription from "@/components/codingChallenge/ProblemDescription.vue";
import CodeEditor from "@/components/codingChallenge/CodeEditor.vue";
import OutputPanel from "@/components/codingChallenge/OutputPanel.vue";
import { executeCode } from "@/piston";

export default {
    name: "CodingChallenge",
    props: {
        challenge: {
            type: Object,
            
        },
    },
    components: {
        ProblemDescription,
        CodeEditor,
        OutputPanel,
    },
    
    data() {
        return {
            outputContent: "",
            outputStatus: "pending",
            outputStatusText: "Ready",
            selectedLanguage: "javascript",
            currentProblem: {
                id: 1,
                title: "Two Sum",
                difficulty: "Easy",
                description: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.",
                examples: [
                    {
                        input: "nums = [2,7,11,15], target = 9",
                        output: "[0,1]",
                        explanation: "Because nums[0] + nums[1] == 9, we return [0, 1].",
                    },
                ],
                constraints: ["2 <= nums.length <= 10^4", "-10^9 <= nums[i] <= 10^9", "-10^9 <= target <= 10^9", "Only one valid answer exists"],
                starterCode: {
                    javascript: "function twoSum(nums, target) {\n  // Your code here\n}",
                    python: "def twoSum(nums, target):\n    # Your code here\n    pass",
                    java: "public class Solution {\n    public int[] twoSum(int[] nums, int target) {\n        // Your code here\n    }\n}",
                    cpp: "class Solution {\npublic:\n    vector<int> twoSum(vector<int>& nums, int target) {\n        // Your code here\n    }\n};",
                },
                testCases: [
                    {
                        input: "[2,7,11,15]",
                        target: 9,
                        expected: "[0,1]",
                    },
                    {
                        input: "[3,2,4]",
                        target: 6,
                        expected: "[1,2]",
                    },
                ],
            },
        };
    },
    methods: {
        async runCode(code) {
            try {
                this.setOutputStatus("running", "Running...");
                const result = await executeCode(code, this.selectedLanguage, this.formatTestInput());
                this.handleResult(result);
            } catch (error) {
                this.handleError(error);
            }
        },

        formatTestInput() {
            const testCase = this.currentProblem.testCases[0];
            return JSON.stringify({
                nums: JSON.parse(testCase.input),
                target: testCase.target,
            });
        },

        handleResult(result) {
            // First handle API failures
            if (!result.success) {
                this.setOutputStatus("error", "API Error");
                this.outputContent = result.output;
                return;
            }

            // Handle successful execution
            const hasError = result.stderr?.trim().length > 0 || (result.exitCode !== 0 && result.exitCode !== undefined);

            if (hasError) {
                this.setOutputStatus("error", `Error ${result.exitCode ?? ""}`.trim());
                this.outputContent = result.stderr || result.stdout || "Unknown error";
            } else {
                // Check output against expected result
                try {
                    const testCase = this.currentProblem.testCases[0];
                    const expected = JSON.parse(testCase.expected);
                    const userOutput = JSON.parse(result.stdout.trim());

                    // Deep comparison for arrays
                    const isCorrect = JSON.stringify(expected) === JSON.stringify(userOutput);

                    if (isCorrect) {
                        this.setOutputStatus("success", "All Tests Passed!");
                    } else {
                        this.setOutputStatus("error", "Test Failed");
                        this.outputContent = `Expected: ${testCase.expected}\nGot: ${result.stdout.trim()}`;
                        return;
                    }
                } catch (e) {
                    // If parsing fails, just show the output
                    this.setOutputStatus("success", "Success");
                }

                this.outputContent = result.stdout;
            }
        },

        handleError(error) {
            this.setOutputStatus("error", "Error");
            this.outputContent = error.message;
            console.error("Execution error:", error);
        },

        setOutputStatus(status, text) {
            this.outputStatus = status;
            this.outputStatusText = text;
        },

        handleLanguageChange(newLang) {
            this.selectedLanguage = newLang;
        },
    },
};
</script>