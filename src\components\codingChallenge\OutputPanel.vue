<template>
    <div class="flex flex-col h-2/5 bg-gray-100 rounded-br-md border-t border-gray-300">
        <div class="flex justify-between items-center p-3 border-b border-gray-300">
            <h3 class="text-lg font-semibold text-gray-700">Tests</h3>
            <span
                class="px-2 py-1 text-sm rounded-md"
                :class="{
                    'bg-gray-700 text-gray-400': computedStatus === 'pending',
                    'bg-blue-800 text-blue-200': computedStatus === 'running',
                    'bg-green-500 text-green-200': computedStatus === 'success',
                    'bg-red-600 text-red-200': computedStatus === 'error',
                }"
            >
                {{ computedStatusText }}
            </span>
        </div>
        <div class="flex-1 p-3 overflow-auto font-mono whitespace-pre-wrap text-left" :class="{ 'text-red-400': isError }">
            <pre v-if="content" class="text-gray-700">{{ content }}</pre>
            <div v-else class="italic text-gray-400 p-4">No output generated. Click "Run Code" to execute your solution.</div>
        </div>
    </div>
</template>

<script>
export default {
    name: "OutputPanel",
    props: {
        content: {
            type: String,
            default: "",
        },
        status: {
            type: String,
            default: "pending",
        },
        statusText: {
            type: String,
            default: "Ready",
        },
    },
    computed: {
        isError() {
            return this.status === "error";
        },
        computedStatus() {
            // If no output and status is 'success', show 'pending' instead
            if (!this.content && this.status === "success") {
                return "pending";
            }
            return this.status;
        },
        computedStatusText() {
            if (!this.content && this.status === "success") {
                return "Ready";
            }
            return this.statusText;
        },
    },
};
</script>