<template>
    <div class="w-2/5 bg-gray-100 overflow-y-auto p-6 rounded-l-md border-r border-gray-300 text-gray-700 text-left">
        <div class="flex justify-between items-center mb-6" v-if="problem">
            <h2 class="text-2xl font-bold">Instructions</h2>
        </div>

        <div class="leading-relaxed">
            <p>{{ problem.description }}</p>

            <div class="my-6">
                <h3 class="text-xl font-semibold mb-3">Examples:</h3>
                
                <div class="bg-gray-200 border border-gray-300 rounded-lg p-4 mb-4">
                    <div class="mb-2"><span class="font-semibold">Input:</span> {{ problem.text }}</div>
                    
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProblemPanel",
    props: {
        problem: {
            type: Object,
            required: true,
            default: () => ({
                title: "Loading Problem...",
                description: "",
                examples: [],
                constraints: [],
            }),
        },
    },
    data() {
        return {
            showHint: false,
        };
    },
};
</script>