<template>
  <div class="qst_box" v-show="current_qst - 1 === qst_number">
    <div>
      <div class="header-question">
        <span>Question {{ qst_number + 1 }} / {{ questions_nbr }}
          <!-- <testTimer :duration="duration" :start="start"/> -->
        </span>
        <div class="progress-container">
          <span class="progress-span"></span>
          <div class="progress-bar-full">
            <div class="progress-bar" :style="{ width: progressWidth }"></div>

            <div class="dot"></div>
          </div>
          <!--<span class="progress-span percentage-span">{{ progressWidth }}</span>-->
        </div>
      </div>
    </div>
    <div class="questions-container">
      <span>{{ qst_question }}</span>
      <span v-if="!qst_description?.toLowerCase().includes('png')">{{
        qst_description
      }}</span>
      <img v-else :src="`https://server.go-platform.com/${qst_description}`"
        style="width: 700px; height: fit-content; margin: 0 auto" alt="" />
      <div class="qst_options" v-for="(option, key, index) in qst_options" :key="option + '_' + index">
        <input type="radio" :id="option" :value="option" name="options" @change="handleAnswer(key)"
          :disabled="disabled" />
        <label :for="option">{{ option }}</label>
      </div>
      <!--<button class="cancel">Cancel</button>
        <button class="next" @click="next">Next</button>-->
    </div>
  </div>
</template>

<script>
// import testTimer from "../components/timer.vue";
import { useAssessementStore } from "@/store/assessements";
export default {
  name: "testQuestion",
  components: {
    /* testTimer */
  },
  data() {
    return {
      duration: 60000,
      userAnswer: "",
      // disabled: this.assessementStore.disabled,
      //start: false,
    };
  },

  setup() {
    const assessementStore = useAssessementStore();
    return { assessementStore };
  },
  props: {
    qst_number: { type: Number, required: true },
    qst_options: { type: Object, required: true },
    qst_question: { type: String, required: true },
    correct_answer: { type: String },
    qst_description: { type: String },
    current_qst: { type: Number, required: true },
    start: { type: Boolean, default: false, required: true },
    questions_nbr: { type: Number, required: true },
    testName: { type: String },
  },
  methods: {
    // next(){this.$emit('next_qst')}
    handleAnswer(key) {
      // this.userAnswer= e.target.value
      // const test = this.assessementStore.test
      this.assessementStore.addAnswer(this.testName, this.qst_number + 1, key);
    },
  },
  computed: {
    progressWidth() {
      const ratio = (this.qst_number + 1) / this.questions_nbr;
      const percentage = Math.floor(ratio * 100);
      return percentage + "%";
    },
    disabled() {
      return this.assessementStore.disabled;
    },
  },

  watch: {
    /* current_qst(){
                if(this.current_qst===this.qst_number){this.start=true}
                else(this.start=false)
            }*/
  },
};
</script>

<style lang="scss" scoped>
.qst_box {
  width: 900px;
  height: fit-content;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  gap: 1rem;
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
  margin-bottom: 2rem;
  font-family: "Roboto" !important;

  > :first-child {
    font-weight: 400;
    font-size: 22px;
    line-height: 33px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    span {
      display: flex;
      justify-content: space-between;
    }
  }

  > :first-child::after {
    content: "";
    border: 1.5px solid rgba(217, 217, 217, 0.5);
    width: 100%;
  }

  > :nth-child(2) {
    font-weight: 500;
    font-size: 22px;
    line-height: 33px;
  }

  > :nth-child(3) {
    font-weight: 500;
    font-size: 15px;
    line-height: 22px;
    color: #bbbbbb;
  }

  .qst_options {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    padding-left: 1rem;

    label {
      cursor: pointer;
      margin-left: 15px;
    }

    input {
      cursor: pointer;
    }
  }
}

.questions-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

::-webkit-scrollbar {
  display: none;
}

.progress-bar-full {
  width: 300px;
  height: 5px;
  border-radius: 30px;
  background: #bbbbbb;
  display: flex;
  justify-content: start;
  align-items: center;
}

.progress-bar {
  width: 0%;
  height: 100%;
  background: #00aef0;
  transition: width 1s ease;
  border-radius: 30px;
}

/*.progress-dot {
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: center;
  margin-left: -15px;
  margin-bottom: 33px;
}*/

.dot {
  position: relative;
  width: 18px;
  height: 18px;
  background: #00aef0;
  border-radius: 50%;
  margin-left: -15px; // Adjust this value for spacing
  // box-shadow: 0 0 20px 0 rgba(33, 150, 243, 0.5);
  /* Adjust the shadow values as needed */
}

.header-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0px;
}

.progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-span {
  margin-right: 10px;
  font-weight: 500;
  color: #263238;
  font-size: 20px;
}

.percentage-span {
  margin-left: 5px;
  color: #00aef0;
}
</style>
