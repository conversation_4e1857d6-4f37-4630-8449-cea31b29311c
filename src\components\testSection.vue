<template>
  <!-- Here you can use v-for to loop through items  -->
  <vsa-item class="accordion-item">
    <vsa-heading class="accordion-header">
      {{ formattedHeader }}
    </vsa-heading>
    <vsa-content class="accordion-content">
      <slot></slot>
    </vsa-content>
  </vsa-item>
</template>

<script>
import {

  VsaItem,
  VsaHeading,
  VsaContent,
  // VsaIcon
} from 'vue-simple-accordion';
import '../assets/styles/vue-simple-accordion.css';
export default {
  name: "TestSection",
  props: {
    header: {
      type: String,
      required: true,
    },
  },
  components: {
    VsaItem,
    VsaHeading,
    VsaContent,
    // VsaIcon
  },
  computed: {
    formattedHeader() {
      // Split the string into words
      const words = this.header.split(' ');

      // Capitalize the first letter of each word
      const formattedWords = words.map(word => {
        // Check if the word is an acronym (all uppercase)
        if (word === word.toUpperCase()) {
          return word; // Keep acronyms as they are
        } else {
          // Capitalize the first letter and convert the rest to lowercase
          return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
      });

      // Join the words back into a string
      return formattedWords.join(' ');
    }
  },
}
</script>

<style lang="scss" scoped></style>