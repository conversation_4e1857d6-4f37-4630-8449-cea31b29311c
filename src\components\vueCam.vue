<template>
  <vue-camera
    ref="camera"
    @face-detected="onFaceDetected"
    @photo="onPhotoTaken"
  />
</template>

<script>
import { VueCamera } from "vue-camera-lib";

export default {
  components: { VueCamera },
  methods: {
    onFaceDetected(hasFace) {
      console.log("Face detected:", hasFace);
    },
    onPhotoTaken(image) {
      this.$emit("photo-captured", image);
    },
  },
};
</script>