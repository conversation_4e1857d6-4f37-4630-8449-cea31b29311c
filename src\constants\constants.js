// API Configuration Constants
export const API_CONFIG = {
  BASE_URL: "https://server.go-platform.com",
  ENDPOINTS: {
    // Assessment endpoints
    CANDIDATE_ASSESSMENTS: "/AssessmentTest/candidatesAssessments",
    EVALUATE_CANDIDATE: "/AssessmentTest/evaluateCandidate",
    CANDIDATE_RATING: "/AssessmentTest/candidateRating",
    SUBMIT_SCREENERS: "/AssessmentTest/submitScreeners",
    
    // Candidate endpoints
    CANDIDATES_POST: "/candidates/post",
    GET_AVATAR: "/candidates/get-avatar",
    UPDATE_CANDIDATE: "/candidates/updateCandidate",
    INVITE_CANDIDATE: "/inviteCandidate/candidate",
    
    // Evaluation endpoints
    EVALUATE_CODE: "/evaluate/evaluate",
    
    // Anti-cheat endpoints
    CREATE_CHEATER: "/anticheat/create-cheater",
    CREATE_PHOTO: "/anticheat/create-photo",
    
    // Face detection endpoints
    CHECK_AVATAR: "/face-detection/check-avatar",
    
    // Certificate endpoints
    CANDIDATE_CERTIFICATE: "/certificate/candidateCertificate",
    CANDIDATE_CERTIFICATE_IMG: "/certificate/candidateCertificateImg",
    
    // LinkedIn endpoints
    LINKEDIN_AUTH: "/linkedIn/auth",
    
    // Job positions endpoints
    JOB_POSITIONS_ALL: "/jobPositions/all"
  }
};

// Helper function to build full URL
export const buildApiUrl = (endpoint) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Helper function to build full URL with parameters
export const buildApiUrlWithParams = (endpoint, params = {}) => {
  const url = new URL(`${API_CONFIG.BASE_URL}${endpoint}`);
  Object.keys(params).forEach(key => {
    url.searchParams.append(key, params[key]);
  });
  return url.toString();
};
