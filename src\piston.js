import axios from "axios";

export const executeCode = async (code, language, testInput) => {
    try {
        // Define language versions based on Piston API requirements
        const versions = {
            javascript: "18.15.0",
            python: "3.10.0",
            java: "15.0.2",
            cpp: "10.2.0",
        };

        // Create request configuration based on language
        const config = {
            language: language,
            version: versions[language],
            files: [{ content: code }],
            stdin: testInput,
        };

        // Make API request
        const response = await axios.post("https://emkc.org/api/v2/piston/execute", config);

        return {
            success: true,
            ...response.data.run,
        };
    } catch (error) {
        console.error("Piston API error:", error);
        return {
            success: false,
            output: error.response?.data?.message || error.message,
            exitCode: -1,
        };
    }
};